import { IPurchasedFileBase, IScheduleType, IScheduleTypeInfo, ScheduleKey } from '../titles/types';

export const scheduleTypes: IScheduleType[] = [
    {
        key: ScheduleKey.titlePlan,
        name: 'Purchase Title Plans',
        isDisabled: false,
        isComingSoon: false,
        isNew: false,
        isBeta: true,
        url: '/api/titles/schedules/title-plan/ABCDEFG',
    },
    {
        key: ScheduleKey.linkedDocuments,
        name: 'Purchase Linked Documents',
        isDisabled: true,
        isComingSoon: true,
        isNew: false,
        isBeta: false,
        url: '/api/titles/schedules/linked-documents/ABCDEFG',
    },
    {
        key: ScheduleKey.appEnquiry,
        name: 'Check for Application Enquiries',
        isDisabled: false,
        isComingSoon: false,
        isNew: false,
        isBeta: false,
        url: '/api/titles/schedules/app-enquiry/ABCDEFG',
    },
    {
        key: ScheduleKey.discharge,
        name: 'Check for Discharge Activities',
        isDisabled: false,
        isComingSoon: false,
        isNew: false,
        isBeta: false,
        url: '/api/titles/schedules/discharge/ABCDEFG',
    },
    {
        key: ScheduleKey.companiesHouse,
        name: 'Check Companies House for Proprietors',
        isDisabled: false,
        isComingSoon: false,
        isNew: true,
        isBeta: false,
        url: '/api/titles/schedules/companies-house/ABCDEFG',
    },
];

export const scheduleTypeInfo: IScheduleTypeInfo = {
    key: ScheduleKey.titlePlan,
    type: 'Title Plan',
    description: `Purchase and incorporate <b>3</b> title plans into your report directly from HM Land Registry.
                You will also be able to download these PDFs separately.`,
    unitCost: '£3',
    totalCost: '£9.00',
    hasPreview: true,
    provider: 'HM Land Registry',
    isUpdated: true,
    isEmpty: false,
    allPurchased: false,
    messages: [],
    items: [
        {
            id: 'Aed9teishooyii4weemiboo7Eevouc',
            reference: 'ABC1267579',
            class: '',
            ownership: 'Freehold',
            address: 'Number 10, Downing Street, London',
            purchaseCost: 3,
            analysisCost: 0,
            offerPurchase: true,
            offerAnalysis: false,
            documentId: 'testId_Aed9teishooyii4weemiboo7Eevouc',
            kind: ScheduleKey.titlePlan,
            isPurchased: false,
            isAvailable: false,
            file: null,
        },
        {
            id: 'vooH3righoa7PahYaes3emei4arooy',
            reference: 'EFG1267579',
            class: '',
            ownership: 'Leasehold',
            address: 'Number 11, Downing Street, London',
            purchaseCost: 3,
            analysisCost: 0,
            offerPurchase: true,
            offerAnalysis: false,
            documentId: 'testId_vooH3righoa7PahYaes3emei4arooy',
            kind: ScheduleKey.titlePlan,
            isPurchased: false,
            isAvailable: false,
            file: null,
        },
        {
            id: 'zeeWahno9eeth9ahno4eipae3biz9g',
            reference: 'HIJK267579',
            class: '',
            ownership: 'Freehold',
            address: 'Number 12, Downing Street, London',
            purchaseCost: 3,
            analysisCost: 0,
            offerPurchase: true,
            offerAnalysis: false,
            documentId: 'testId_zeeWahno9eeth9ahno4eipae3biz9g',
            kind: ScheduleKey.titlePlan,
            isPurchased: false,
            isAvailable: false,
            file: null,
        },
    ],
};

export const schedulesPurchaseResponses: IPurchasedFileBase[] = [
    {
        cost: '\u00a30',
        documentId: 'Y8oSwGd4qUJtzBFCSXi5py',
        folderId: '4HSsSBxE98Kv5t6BRM3Vcx',
        isError: true,
        kind: 'title-register',
        message: 'Service is not currently available. System has queued your request, please poll at specified time.',
        purchasedAt: '2020-11-16T12:08:43.801026',
        reference: 'AGL5895',
    },
    {
        cost: '\u00a30',
        documentId: 'jFikERxDJ4ecVakfJSSjZQ',
        folderId: '4HSsSBxE98Kv5t6BRM3Vcx',
        isError: true,
        kind: 'title-register',
        message: 'Service is not currently available. System has queued your request, please poll at specified time.',
        purchasedAt: '2020-11-16T12:08:40.037286',
        reference: 'BM389539',
    },
    {
        cost: '£3',
        documentId: '4SpGVbWb33ALzdPRcrDsxd',
        folderId: 'nxqVo9wtZn5TJPGqNyKMDA',
        isError: false,
        kind: 'title-register',
        purchasedAt: '2020-11-16T11:54:45.344299',
        reference: 'TY313251',
        message: '',
    },
];
